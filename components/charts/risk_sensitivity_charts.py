"""
Risk & Sensitivity Charts
========================

Specialized chart components for risk analysis and sensitivity studies including
heatmaps, tornado diagrams, Monte Carlo distributions, and risk dashboards.
"""

import flet as ft
from typing import Dict, Any, Optional, List, Tuple
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')
import numpy as np
import pandas as pd
import seaborn as sns
import io
import base64
from pathlib import Path
import logging
from datetime import datetime


class RiskSensitivityCharts:
    """Risk and sensitivity analysis chart components."""
    
    def __init__(self, chart_factory):
        self.chart_factory = chart_factory
        self.logger = logging.getLogger(__name__)
        
        # Use chart factory's styling and export settings
        self.professional_colors = chart_factory.professional_colors
        self.professional_style = chart_factory.professional_style
        self.export_settings = chart_factory.export_settings
    
    def create_sensitivity_heatmap(self, 
                                 sensitivity_data: pd.DataFrame = None, 
                                 title: str = "Sensitivity Analysis",
                                 save_path: Optional[Path] = None) -> Tuple[ft.Container, bytes]:
        """Create sensitivity heatmap showing parameter impacts."""
        try:
            fig, ax = plt.subplots(figsize=(12, 8))
            
            # Sample sensitivity data if none provided
            if sensitivity_data is None:
                parameters = ['CAPEX', 'Revenue', 'OPEX', 'Discount Rate', 'Capacity Factor', 'Debt Rate']
                scenarios = ['P10', 'P25', 'P50', 'P75', 'P90']
                
                # Generate sample IRR sensitivity data
                np.random.seed(42)
                data = np.random.normal(0.15, 0.03, (len(parameters), len(scenarios)))
                sensitivity_data = pd.DataFrame(data, index=parameters, columns=scenarios)
            
            # Create heatmap
            sns.heatmap(sensitivity_data, annot=True, fmt='.2%', cmap='RdYlGn', 
                       center=sensitivity_data.mean().mean(), ax=ax,
                       cbar_kws={'label': 'IRR'})
            
            # Formatting
            ax.set_title(title, fontsize=16, fontweight='bold')
            ax.set_xlabel('Probability Scenarios', fontsize=12, fontweight='bold')
            ax.set_ylabel('Input Parameters', fontsize=12, fontweight='bold')
            
            plt.tight_layout()
            
            if save_path:
                self.chart_factory._save_chart_to_file(fig, save_path, title)
            
            chart_bytes = self.chart_factory._get_chart_bytes(fig)
            ui_component = self.chart_factory._create_ui_component(fig, width=1000, height=600)
            plt.close(fig)
            
            return ui_component, chart_bytes
            
        except Exception as e:
            self.logger.error(f"Error creating sensitivity heatmap: {e}")
            return self.chart_factory._create_error_chart(title, str(e))
    
    def create_tornado_diagram(self,
                             sensitivity_data: Dict[str, Dict[str, float]] = None,
                             title: str = "Tornado Diagram - IRR Sensitivity",
                             save_path: Optional[Path] = None) -> Tuple[ft.Container, bytes]:
        """Create tornado diagram for sensitivity analysis."""
        try:
            fig, ax = plt.subplots(figsize=(12, 8))
            
            # Sample sensitivity data if none provided
            if not sensitivity_data:
                sensitivity_data = {
                    'Revenue': {'low': -0.08, 'high': 0.12},
                    'CAPEX': {'low': 0.06, 'high': -0.04},
                    'OPEX': {'low': 0.03, 'high': -0.02},
                    'Capacity Factor': {'low': -0.05, 'high': 0.07},
                    'Discount Rate': {'low': 0.04, 'high': -0.03},
                    'Debt Interest': {'low': 0.02, 'high': -0.015}
                }
            
            parameters = list(sensitivity_data.keys())
            low_values = [sensitivity_data[param]['low'] for param in parameters]
            high_values = [sensitivity_data[param]['high'] for param in parameters]
            
            # Calculate ranges for sorting
            ranges = [abs(high - low) for high, low in zip(high_values, low_values)]
            
            # Sort by impact (largest range first)
            sorted_indices = sorted(range(len(ranges)), key=lambda i: ranges[i], reverse=True)
            parameters = [parameters[i] for i in sorted_indices]
            low_values = [low_values[i] for i in sorted_indices]
            high_values = [high_values[i] for i in sorted_indices]
            
            y_pos = np.arange(len(parameters))
            
            # Create horizontal bars
            for i, (param, low, high) in enumerate(zip(parameters, low_values, high_values)):
                # Low impact (left side)
                ax.barh(i, abs(low), left=min(0, low), height=0.6,
                       color=self.professional_colors['danger_palette'][1], alpha=0.7,
                       label='Downside' if i == 0 else "")
                
                # High impact (right side)
                ax.barh(i, abs(high), left=max(0, min(high, 0)), height=0.6,
                       color=self.professional_colors['success_palette'][1], alpha=0.7,
                       label='Upside' if i == 0 else "")
                
                # Add value labels
                ax.text(low - 0.005, i, f'{low:.1%}', ha='right', va='center', fontweight='bold')
                ax.text(high + 0.005, i, f'{high:.1%}', ha='left', va='center', fontweight='bold')
            
            # Add vertical line at zero
            ax.axvline(x=0, color='black', linewidth=1)
            
            # Formatting
            ax.set_yticks(y_pos)
            ax.set_yticklabels(parameters)
            ax.set_xlabel('IRR Impact', fontsize=12, fontweight='bold')
            ax.set_title(title, fontsize=16, fontweight='bold')
            ax.legend(loc='lower right')
            ax.grid(True, alpha=0.3, axis='x')
            
            plt.tight_layout()
            
            if save_path:
                self.chart_factory._save_chart_to_file(fig, save_path, title)
            
            chart_bytes = self.chart_factory._get_chart_bytes(fig)
            ui_component = self.chart_factory._create_ui_component(fig, width=1000, height=600)
            plt.close(fig)
            
            return ui_component, chart_bytes
            
        except Exception as e:
            self.logger.error(f"Error creating tornado diagram: {e}")
            return self.chart_factory._create_error_chart(title, str(e))
    
    def create_monte_carlo_distribution(self,
                                      simulation_results: np.ndarray = None,
                                      title: str = "Monte Carlo Analysis",
                                      save_path: Optional[Path] = None) -> Tuple[ft.Container, bytes]:
        """Create Monte Carlo distribution histogram."""
        try:
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))
            
            # Sample Monte Carlo data if none provided
            if simulation_results is None:
                np.random.seed(42)
                irr_results = np.random.normal(0.15, 0.03, 1000)
                npv_results = np.random.normal(50, 15, 1000)  # Million euros
            else:
                irr_results = simulation_results.get('irr', np.random.normal(0.15, 0.03, 1000))
                npv_results = simulation_results.get('npv', np.random.normal(50, 15, 1000))
            
            # IRR Distribution
            ax1.hist(irr_results * 100, bins=50, alpha=0.7, 
                    color=self.professional_colors['primary_palette'][0], edgecolor='black')
            
            # Add percentile lines
            p10_irr = np.percentile(irr_results * 100, 10)
            p50_irr = np.percentile(irr_results * 100, 50)
            p90_irr = np.percentile(irr_results * 100, 90)
            
            ax1.axvline(p10_irr, color='red', linestyle='--', linewidth=2, label=f'P10: {p10_irr:.1f}%')
            ax1.axvline(p50_irr, color='green', linestyle='-', linewidth=2, label=f'P50: {p50_irr:.1f}%')
            ax1.axvline(p90_irr, color='red', linestyle='--', linewidth=2, label=f'P90: {p90_irr:.1f}%')
            
            ax1.set_xlabel('IRR (%)', fontsize=12, fontweight='bold')
            ax1.set_ylabel('Frequency', fontsize=12, fontweight='bold')
            ax1.set_title('IRR Distribution', fontsize=14, fontweight='bold')
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            
            # NPV Distribution
            ax2.hist(npv_results, bins=50, alpha=0.7, 
                    color=self.professional_colors['success_palette'][1], edgecolor='black')
            
            # Add percentile lines
            p10_npv = np.percentile(npv_results, 10)
            p50_npv = np.percentile(npv_results, 50)
            p90_npv = np.percentile(npv_results, 90)
            
            ax2.axvline(p10_npv, color='red', linestyle='--', linewidth=2, label=f'P10: €{p10_npv:.1f}M')
            ax2.axvline(p50_npv, color='green', linestyle='-', linewidth=2, label=f'P50: €{p50_npv:.1f}M')
            ax2.axvline(p90_npv, color='red', linestyle='--', linewidth=2, label=f'P90: €{p90_npv:.1f}M')
            
            ax2.set_xlabel('NPV (€ Millions)', fontsize=12, fontweight='bold')
            ax2.set_ylabel('Frequency', fontsize=12, fontweight='bold')
            ax2.set_title('NPV Distribution', fontsize=14, fontweight='bold')
            ax2.legend()
            ax2.grid(True, alpha=0.3)
            
            fig.suptitle(title, fontsize=16, fontweight='bold')
            plt.tight_layout()
            
            if save_path:
                self.chart_factory._save_chart_to_file(fig, save_path, title)
            
            chart_bytes = self.chart_factory._get_chart_bytes(fig)
            ui_component = self.chart_factory._create_ui_component(fig, width=1200, height=600)
            plt.close(fig)
            
            return ui_component, chart_bytes
            
        except Exception as e:
            self.logger.error(f"Error creating Monte Carlo distribution: {e}")
            return self.chart_factory._create_error_chart(title, str(e))
    
    def create_risk_dashboard(self,
                            risk_metrics: Dict[str, Any] = None,
                            title: str = "Risk Assessment Dashboard",
                            save_path: Optional[Path] = None) -> Tuple[ft.Container, bytes]:
        """Create comprehensive risk dashboard."""
        try:
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle(title, fontsize=18, fontweight='bold')
            
            # Sample risk data if none provided
            if not risk_metrics:
                risk_metrics = {
                    'var_95': 0.08,  # Value at Risk 95%
                    'cvar_95': 0.12,  # Conditional VaR 95%
                    'probability_loss': 0.15,  # Probability of loss
                    'risk_categories': {
                        'Market Risk': 3.5,
                        'Technology Risk': 2.8,
                        'Financial Risk': 2.1,
                        'Regulatory Risk': 3.2,
                        'Operational Risk': 2.5
                    }
                }
            
            # Chart 1: Risk Metrics Gauge
            risk_levels = ['VaR 95%', 'CVaR 95%', 'P(Loss)']
            risk_values = [risk_metrics.get('var_95', 0.08) * 100,
                          risk_metrics.get('cvar_95', 0.12) * 100,
                          risk_metrics.get('probability_loss', 0.15) * 100]
            
            bars = ax1.bar(risk_levels, risk_values, 
                          color=[self.professional_colors['warning_palette'][i] for i in range(3)], 
                          alpha=0.8)
            
            for bar, value in zip(bars, risk_values):
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                        f'{value:.1f}%', ha='center', va='bottom', fontweight='bold')
            
            ax1.set_title('Key Risk Metrics', fontweight='bold')
            ax1.set_ylabel('Percentage (%)')
            ax1.grid(True, alpha=0.3)
            
            # Chart 2: Risk Categories Radar-style
            categories = list(risk_metrics.get('risk_categories', {}).keys())
            scores = list(risk_metrics.get('risk_categories', {}).values())
            
            bars = ax2.barh(categories, scores, 
                           color=self.professional_colors['danger_palette'][:len(categories)], 
                           alpha=0.7)
            
            for bar, score in zip(bars, scores):
                width = bar.get_width()
                ax2.text(width + 0.1, bar.get_y() + bar.get_height()/2.,
                        f'{score:.1f}', ha='left', va='center', fontweight='bold')
            
            ax2.set_title('Risk Category Assessment', fontweight='bold')
            ax2.set_xlabel('Risk Score (1-5)')
            ax2.set_xlim(0, 5)
            ax2.grid(True, alpha=0.3)
            
            # Chart 3: Risk Timeline (sample)
            months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun']
            risk_trend = [2.8, 3.1, 2.9, 3.3, 3.0, 2.7]
            
            ax3.plot(months, risk_trend, marker='o', linewidth=3, markersize=8,
                    color=self.professional_colors['primary_palette'][0])
            ax3.fill_between(months, risk_trend, alpha=0.3, 
                           color=self.professional_colors['primary_palette'][0])
            
            ax3.set_title('Risk Trend Analysis', fontweight='bold')
            ax3.set_ylabel('Overall Risk Score')
            ax3.grid(True, alpha=0.3)
            
            # Chart 4: Risk vs Return Scatter
            projects = ['Project A', 'Project B', 'Project C', 'Current', 'Project D']
            returns = [12, 15, 18, 14, 16]
            risks = [2.1, 2.8, 3.5, 2.5, 3.1]
            
            colors = [self.professional_colors['success_palette'][1] if r < 3.0 
                     else self.professional_colors['warning_palette'][1] for r in risks]
            
            scatter = ax4.scatter(risks, returns, c=colors, s=200, alpha=0.7, edgecolors='black')
            
            # Highlight current project
            current_idx = projects.index('Current')
            ax4.scatter(risks[current_idx], returns[current_idx], 
                       c='red', s=300, marker='*', edgecolors='black', label='Current Project')
            
            ax4.set_xlabel('Risk Score')
            ax4.set_ylabel('Expected Return (%)')
            ax4.set_title('Risk vs Return Analysis', fontweight='bold')
            ax4.legend()
            ax4.grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            if save_path:
                self.chart_factory._save_chart_to_file(fig, save_path, title)
            
            chart_bytes = self.chart_factory._get_chart_bytes(fig)
            ui_component = self.chart_factory._create_ui_component(fig, width=1400, height=900)
            plt.close(fig)
            
            return ui_component, chart_bytes
            
        except Exception as e:
            self.logger.error(f"Error creating risk dashboard: {e}")
            return self.chart_factory._create_error_chart(title, str(e))
