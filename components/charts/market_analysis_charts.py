"""
Market Analysis Charts
=====================

Specialized chart components for market analysis including competitive positioning,
location radar charts, and market dashboards.
"""

import flet as ft
from typing import Dict, Any, Optional, List, Tuple
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')
import numpy as np
import pandas as pd
import io
import base64
from pathlib import Path
import logging
from datetime import datetime


class MarketAnalysisCharts:
    """Market analysis chart components."""
    
    def __init__(self, chart_factory):
        self.chart_factory = chart_factory
        self.logger = logging.getLogger(__name__)
        
        # Use chart factory's styling and export settings
        self.professional_colors = chart_factory.professional_colors
        self.professional_style = chart_factory.professional_style
        self.export_settings = chart_factory.export_settings
    
    def create_competitive_positioning_map(self, 
                                         competitive_data: Dict[str, Dict[str, float]] = None, 
                                         title: str = "Competitive Positioning Map",
                                         save_path: Optional[Path] = None) -> Tuple[ft.Container, bytes]:
        """Create competitive positioning scatter plot."""
        try:
            fig, ax = plt.subplots(figsize=(12, 8))
            
            # Sample competitive data if none provided
            if not competitive_data:
                competitive_data = {
                    'Our Project': {'cost_competitiveness': 8.5, 'market_position': 7.2, 'size': 100},
                    'Competitor A': {'cost_competitiveness': 7.8, 'market_position': 8.1, 'size': 150},
                    'Competitor B': {'cost_competitiveness': 6.5, 'market_position': 6.8, 'size': 80},
                    'Competitor C': {'cost_competitiveness': 9.1, 'market_position': 5.9, 'size': 120},
                    'Competitor D': {'cost_competitiveness': 7.2, 'market_position': 7.8, 'size': 90},
                    'Market Leader': {'cost_competitiveness': 8.8, 'market_position': 9.2, 'size': 200}
                }
            
            # Extract data
            companies = list(competitive_data.keys())
            x_values = [competitive_data[comp]['cost_competitiveness'] for comp in companies]
            y_values = [competitive_data[comp]['market_position'] for comp in companies]
            sizes = [competitive_data[comp].get('size', 100) for comp in companies]
            
            # Create scatter plot with different colors
            colors = []
            for i, company in enumerate(companies):
                if 'Our Project' in company:
                    colors.append(self.professional_colors['success_palette'][1])
                elif 'Leader' in company:
                    colors.append(self.professional_colors['primary_palette'][0])
                else:
                    colors.append(self.professional_colors['secondary_palette'][i % 3])
            
            scatter = ax.scatter(x_values, y_values, s=sizes, c=colors, alpha=0.7, edgecolors='black')
            
            # Add company labels
            for i, company in enumerate(companies):
                ax.annotate(company, (x_values[i], y_values[i]), 
                           xytext=(5, 5), textcoords='offset points', 
                           fontsize=10, fontweight='bold')
            
            # Add quadrant lines
            ax.axhline(y=7.5, color='gray', linestyle='--', alpha=0.5)
            ax.axvline(x=7.5, color='gray', linestyle='--', alpha=0.5)
            
            # Add quadrant labels
            ax.text(9.5, 9.5, 'Leaders', fontsize=12, fontweight='bold', 
                   bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.7))
            ax.text(6.0, 9.5, 'Challengers', fontsize=12, fontweight='bold',
                   bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.7))
            ax.text(9.5, 6.0, 'Niche Players', fontsize=12, fontweight='bold',
                   bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.7))
            ax.text(6.0, 6.0, 'Followers', fontsize=12, fontweight='bold',
                   bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.7))
            
            # Formatting
            ax.set_xlabel('Cost Competitiveness', fontsize=12, fontweight='bold')
            ax.set_ylabel('Market Position', fontsize=12, fontweight='bold')
            ax.set_title(title, fontsize=16, fontweight='bold')
            ax.grid(True, alpha=0.3)
            ax.set_xlim(5, 10)
            ax.set_ylim(5, 10)
            
            plt.tight_layout()
            
            if save_path:
                self.chart_factory._save_chart_to_file(fig, save_path, title)
            
            chart_bytes = self.chart_factory._get_chart_bytes(fig)
            ui_component = self.chart_factory._create_ui_component(fig, width=1000, height=700)
            plt.close(fig)
            
            return ui_component, chart_bytes
            
        except Exception as e:
            self.logger.error(f"Error creating competitive positioning map: {e}")
            return self.chart_factory._create_error_chart(title, str(e))
    
    def create_location_comparison_radar(self,
                                       location_data: Dict[str, Dict[str, float]] = None,
                                       title: str = "Location Comparison Radar",
                                       save_path: Optional[Path] = None) -> Tuple[ft.Container, bytes]:
        """Create radar chart for location comparison."""
        try:
            fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))
            
            # Sample location data if none provided
            if not location_data:
                location_data = {
                    'Ouarzazate': {'solar_resource': 9.2, 'grid_access': 8.5, 'land_cost': 7.8, 
                                  'regulatory': 8.0, 'logistics': 7.5, 'labor': 7.2},
                    'Noor Midelt': {'solar_resource': 8.8, 'grid_access': 8.0, 'land_cost': 8.2, 
                                   'regulatory': 8.5, 'logistics': 7.8, 'labor': 7.5},
                    'Tata': {'solar_resource': 9.0, 'grid_access': 7.2, 'land_cost': 8.5, 
                            'regulatory': 7.8, 'logistics': 6.8, 'labor': 7.0}
                }
            
            # Get categories (should be same for all locations)
            categories = list(next(iter(location_data.values())).keys())
            N = len(categories)
            
            # Compute angle for each category
            angles = [n / float(N) * 2 * np.pi for n in range(N)]
            angles += angles[:1]  # Complete the circle
            
            # Plot data for each location
            colors = self.professional_colors['primary_palette'][:len(location_data)]
            
            for i, (location, data) in enumerate(location_data.items()):
                values = [data[cat] for cat in categories]
                values += values[:1]  # Complete the circle
                
                ax.plot(angles, values, 'o-', linewidth=2, label=location, 
                       color=colors[i % len(colors)])
                ax.fill(angles, values, alpha=0.25, color=colors[i % len(colors)])
            
            # Add category labels
            ax.set_xticks(angles[:-1])
            ax.set_xticklabels([cat.replace('_', ' ').title() for cat in categories])
            
            # Set y-axis limits and labels
            ax.set_ylim(0, 10)
            ax.set_yticks([2, 4, 6, 8, 10])
            ax.set_yticklabels(['2', '4', '6', '8', '10'])
            ax.grid(True)
            
            # Add title and legend
            ax.set_title(title, size=16, fontweight='bold', pad=20)
            ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
            
            plt.tight_layout()
            
            if save_path:
                self.chart_factory._save_chart_to_file(fig, save_path, title)
            
            chart_bytes = self.chart_factory._get_chart_bytes(fig)
            ui_component = self.chart_factory._create_ui_component(fig, width=1000, height=800)
            plt.close(fig)
            
            return ui_component, chart_bytes
            
        except Exception as e:
            self.logger.error(f"Error creating location comparison radar: {e}")
            return self.chart_factory._create_error_chart(title, str(e))
    
    def create_market_analysis_dashboard(self,
                                       market_data: Dict[str, Dict] = None,
                                       title: str = "Market Analysis Dashboard",
                                       save_path: Optional[Path] = None) -> Tuple[ft.Container, bytes]:
        """Create comprehensive market analysis dashboard."""
        try:
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle(title, fontsize=18, fontweight='bold')
            
            # Sample market data if none provided
            if not market_data:
                market_data = {
                    'market_size': {'2020': 45, '2021': 52, '2022': 58, '2023': 65, '2024': 72},
                    'market_share': {'Company A': 25, 'Company B': 20, 'Company C': 15, 'Others': 40},
                    'price_trends': {'Q1': 42, 'Q2': 45, 'Q3': 43, 'Q4': 47},
                    'regional_demand': {'North': 30, 'South': 25, 'East': 20, 'West': 25}
                }
            
            # Chart 1: Market Size Growth
            market_size = market_data.get('market_size', {})
            years = list(market_size.keys())
            sizes = list(market_size.values())
            
            ax1.plot(years, sizes, marker='o', linewidth=3, markersize=8,
                    color=self.professional_colors['primary_palette'][0])
            ax1.fill_between(years, sizes, alpha=0.3, 
                           color=self.professional_colors['primary_palette'][0])
            
            # Add trend line
            x_numeric = range(len(years))
            z = np.polyfit(x_numeric, sizes, 1)
            p = np.poly1d(z)
            ax1.plot(years, p(x_numeric), "--", color='red', alpha=0.8, linewidth=2)
            
            ax1.set_title('Market Size Growth (GW)', fontweight='bold')
            ax1.set_ylabel('Capacity (GW)')
            ax1.grid(True, alpha=0.3)
            
            # Chart 2: Market Share Pie
            market_share = market_data.get('market_share', {})
            companies = list(market_share.keys())
            shares = list(market_share.values())
            
            colors = self.professional_colors['corporate_palette'][:len(companies)]
            wedges, texts, autotexts = ax2.pie(shares, labels=companies, colors=colors,
                                              autopct='%1.1f%%', startangle=90)
            
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontweight('bold')
            
            ax2.set_title('Market Share Distribution', fontweight='bold')
            
            # Chart 3: Price Trends
            price_trends = market_data.get('price_trends', {})
            quarters = list(price_trends.keys())
            prices = list(price_trends.values())
            
            bars = ax3.bar(quarters, prices, 
                          color=self.professional_colors['financial_palette'][:len(quarters)], 
                          alpha=0.8)
            
            # Add value labels
            for bar, price in zip(bars, prices):
                height = bar.get_height()
                ax3.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                        f'€{price}', ha='center', va='bottom', fontweight='bold')
            
            ax3.set_title('Price Trends (€/MWh)', fontweight='bold')
            ax3.set_ylabel('Price (€/MWh)')
            ax3.grid(True, alpha=0.3, axis='y')
            
            # Chart 4: Regional Demand
            regional_demand = market_data.get('regional_demand', {})
            regions = list(regional_demand.keys())
            demands = list(regional_demand.values())
            
            bars = ax4.barh(regions, demands, 
                           color=self.professional_colors['success_palette'][:len(regions)], 
                           alpha=0.8)
            
            # Add value labels
            for bar, demand in zip(bars, demands):
                width = bar.get_width()
                ax4.text(width + 0.5, bar.get_y() + bar.get_height()/2.,
                        f'{demand}%', ha='left', va='center', fontweight='bold')
            
            ax4.set_title('Regional Demand Distribution', fontweight='bold')
            ax4.set_xlabel('Demand (%)')
            ax4.grid(True, alpha=0.3, axis='x')
            
            plt.tight_layout()
            
            if save_path:
                self.chart_factory._save_chart_to_file(fig, save_path, title)
            
            chart_bytes = self.chart_factory._get_chart_bytes(fig)
            ui_component = self.chart_factory._create_ui_component(fig, width=1400, height=900)
            plt.close(fig)
            
            return ui_component, chart_bytes
            
        except Exception as e:
            self.logger.error(f"Error creating market analysis dashboard: {e}")
            return self.chart_factory._create_error_chart(title, str(e))
