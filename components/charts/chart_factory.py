"""
Chart Factory
=============

Factory class for creating various chart components.
"""

import flet as ft
from typing import Dict, Any, Optional, List, Tuple
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')  # Set backend for thread-safe operation
import matplotlib.patches as patches
import numpy as np
import io
import base64
from pathlib import Path
import logging
from config.export_config import ExportConfig
from datetime import datetime
import seaborn as sns
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import plotly.io as pio
from matplotlib.patches import Rectangle
from matplotlib.colors import LinearSegmentedColormap
import warnings
warnings.filterwarnings('ignore')

# Import specialized chart modules
from .financial_analysis_charts import FinancialAnalysisCharts
from .risk_sensitivity_charts import RiskSensitivityCharts
from .project_management_charts import ProjectManagementCharts
from .market_analysis_charts import MarketAnalysisCharts
from .basic_charts import BasicCharts

# Import benchmarks service
try:
    from services.industry_benchmarks_service import IndustryBenchmarksService, TechnologyType, RegionType
except ImportError:
    # Fallback if service not available
    IndustryBenchmarksService = None
    TechnologyType = None
    RegionType = None

# Set professional styling
plt.style.use('seaborn-v0_8-whitegrid')
sns.set_palette("husl")


class ChartFactory:
    """Factory for creating professional chart components with advanced styling."""

    def __init__(self, export_config: Optional[ExportConfig] = None):
        # Initialize export configuration
        self.export_config = export_config or ExportConfig()

        # Get color palette from configuration
        self.default_colors = self.export_config.get_color_palette()
        self.logger = logging.getLogger(__name__)

        # Initialize benchmarks service
        self.benchmarks_service = IndustryBenchmarksService() if IndustryBenchmarksService else None

        # Get chart export settings from configuration
        chart_settings = self.export_config.get_chart_settings()
        quality_settings = self.export_config.get_export_quality_settings()

        # Create export settings - remove unsupported parameters for matplotlib
        base_export_settings = {
            'dpi': quality_settings['dpi'],
            'format': chart_settings['format'].lower(),
            'bbox_inches': 'tight',
            'facecolor': chart_settings['background_color'],
            'edgecolor': 'none',
            'transparent': chart_settings['transparent_background']
        }

        # Store additional settings for potential future use (not passed to matplotlib)
        self.additional_settings = {
            'optimize': quality_settings.get('optimize', False),
            'progressive': quality_settings.get('progressive', True)
        }

        self.export_settings = base_export_settings

        # Professional color schemes
        self.professional_colors = {
            'primary_palette': ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b'],
            'financial_palette': ['#2E8B57', '#DC143C', '#4169E1', '#FF8C00', '#9932CC', '#8B4513'],
            'risk_palette': ['#228B22', '#FFD700', '#FF4500', '#DC143C', '#8B0000'],
            'corporate_palette': ['#003366', '#0066CC', '#66B2FF', '#B3D9FF', '#E6F3FF'],
            'seaborn_palette': sns.color_palette("husl", 10).as_hex(),
            'success_palette': ['#2E8B57', '#32CD32', '#90EE90', '#98FB98', '#F0FFF0'],
            'danger_palette': ['#8B0000', '#DC143C', '#FF6347', '#FFA07A', '#FFE4E1'],
            'warning_palette': ['#FF8C00', '#FFD700', '#FFFF00', '#FFFFE0', '#FFFACD'],
            'secondary_palette': ['#4682B4', '#87CEEB', '#B0E0E6', '#E0F6FF', '#F0F8FF'],
            'text_primary': '#2C3E50',
            'text_secondary': '#7F8C8D',
            'background_light': '#FAFAFA',
            'background_dark': '#34495E'
        }

        # Professional styling defaults with fallback fonts
        available_fonts = ['Arial', 'DejaVu Sans', 'Liberation Sans', 'Helvetica', 'sans-serif']
        font_family = 'DejaVu Sans'  # Default fallback

        # Try to find a suitable font
        try:
            import matplotlib.font_manager as fm
            system_fonts = {f.name for f in fm.fontManager.ttflist}
            for font in available_fonts:
                if font in system_fonts or font == 'sans-serif':
                    font_family = font
                    break
        except Exception:
            # If font detection fails, use default
            pass

        self.professional_style = {
            'figure_size': (12, 8),
            'title_size': 16,
            'label_size': 12,
            'tick_size': 10,
            'legend_size': 11,
            'line_width': 2.5,
            'marker_size': 8,
            'grid_alpha': 0.3,
            'bar_alpha': 0.8,
            'font_family': font_family,
            'title_weight': 'bold',
            'spine_width': 1.2
        }

        # Configure matplotlib for professional output
        plt.rcParams.update({
            'font.family': self.professional_style['font_family'],
            'font.size': self.professional_style['tick_size'],
            'axes.titlesize': self.professional_style['title_size'],
            'axes.labelsize': self.professional_style['label_size'],
            'xtick.labelsize': self.professional_style['tick_size'],
            'ytick.labelsize': self.professional_style['tick_size'],
            'legend.fontsize': self.professional_style['legend_size'],
            'figure.titlesize': self.professional_style['title_size'],
            'axes.spines.top': False,
            'axes.spines.right': False,
            'axes.grid': True,
            'grid.alpha': self.professional_style['grid_alpha']
        })

        # Initialize specialized chart modules (after colors and styles are defined)
        self.financial_charts = FinancialAnalysisCharts(self)
        self.risk_charts = RiskSensitivityCharts(self)
        self.project_charts = ProjectManagementCharts(self)
        self.market_charts = MarketAnalysisCharts(self)
        self.basic_charts = BasicCharts(self)

    def _save_chart_to_file(self, fig, filepath: Path, title: str = "") -> bool:
        """Save matplotlib figure to file."""
        try:
            filepath.parent.mkdir(parents=True, exist_ok=True)
            fig.savefig(
                filepath,
                **self.export_settings
            )
            self.logger.info(f"Chart saved: {filepath}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to save chart: {e}")
            return False

    def _get_chart_bytes(self, fig) -> bytes:
        """Convert matplotlib figure to bytes."""
        try:
            buffer = io.BytesIO()
            # Create simplified export settings for bytes conversion (PNG format)
            # Only use parameters supported by matplotlib's savefig method
            bytes_export_settings = {
                'format': 'png',
                'dpi': self.export_settings.get('dpi', 300),
                'bbox_inches': 'tight',
                'facecolor': self.export_settings.get('facecolor', 'white'),
                'edgecolor': 'none',
                'transparent': False  # Force non-transparent for bytes
            }
            fig.savefig(buffer, **bytes_export_settings)
            buffer.seek(0)
            return buffer.getvalue()
        except Exception as e:
            self.logger.error(f"Failed to convert chart to bytes: {e}")
            return b''

    def _create_ui_component(self, fig, width: int = 800, height: int = 600) -> ft.Container:
        """Create Flet UI component from matplotlib figure."""
        try:
            chart_bytes = self._get_chart_bytes(fig)
            if chart_bytes:
                return ft.Container(
                    content=ft.Image(
                        src_base64=base64.b64encode(chart_bytes).decode(),
                        width=width,
                        height=height,
                        fit=ft.ImageFit.CONTAIN
                    ),
                    width=width,
                    height=height,
                    alignment=ft.alignment.center
                )
            else:
                return ft.Container(
                    content=ft.Text("Chart generation failed"),
                    width=width,
                    height=height
                )
        except Exception as e:
            self.logger.error(f"Failed to create UI component: {e}")
            return ft.Container(
                content=ft.Text(f"Chart error: {str(e)}"),
                width=width,
                height=height
            )

    def _get_individual_chart_path(self, base_path: Path, chart_name: str) -> Optional[Path]:
        """Get individual chart file path."""
        try:
            if base_path.is_dir():
                return base_path / f"{chart_name}.png"
            else:
                # If base_path is a file, create a sibling file
                return base_path.parent / f"{base_path.stem}_{chart_name}.png"
        except Exception as e:
            self.logger.error(f"Failed to create chart path: {e}")
            return None

    def create_financial_structure_chart(self,
                                       assumptions: Dict[str, Any] = None,
                                       title: str = "Project Financial Structure",
                                       save_path: Optional[Path] = None,
                                       financial_results: Dict[str, Any] = None) -> Tuple[ft.Container, bytes]:
        """Create pie chart showing financial structure breakdown with real data."""
        fig, ax = plt.subplots(1, 1, figsize=(10, 8))

        # Try to use real financial data first
        if financial_results and 'kpis' in financial_results:
            kpis = financial_results['kpis']
            assumptions_data = financial_results.get('assumptions', assumptions or {})

            # Extract real financial data
            capex_meur = kpis.get('Total_capex', assumptions_data.get('capex_meur', 100)) / 1e6
            equity_percentage = 1 - assumptions_data.get('debt_ratio', 0.75)
            total_debt_meur = capex_meur * assumptions_data.get('debt_ratio', 0.75)

            self.logger.info("Using real financial data for financial structure chart")
        else:
            # Fallback to provided assumptions or defaults
            assumptions_data = assumptions or {}
            capex_meur = assumptions_data.get('capex_meur', 100)
            equity_percentage = assumptions_data.get('equity_percentage', 1 - assumptions_data.get('debt_ratio', 0.75))
            total_debt_meur = capex_meur * assumptions_data.get('debt_ratio', 0.75)

            self.logger.warning("Using fallback assumptions for financial structure chart")

        # Extract SIMEST split values
        simest_total = assumptions_data.get('simest_total_facility_meur', 0.0)
        simest_grant = assumptions_data.get('simest_grant_meur', 0.0)
        simest_soft = assumptions_data.get('simest_soft_loan_meur', 0.0)

        # Other grants
        grant_morocco = assumptions_data.get('grant_meur_masen', 0.0)
        grant_connection = assumptions_data.get('grant_meur_connection', 0.0)
        grant_cri = assumptions_data.get('grant_meur_cri', 0.0)
        other_grants = grant_morocco + grant_connection + grant_cri

        # Equity and debt split
        equity_meur = capex_meur * equity_percentage
        commercial_debt = max(total_debt_meur - simest_soft, 0.0)

        # Prepare slices
        labels = []
        sizes = []
        colors = []

        # Equity slice
        if equity_meur > 0:
            labels.append(f'Equity\n€{equity_meur:.1f}M\n({equity_meur/capex_meur*100:.1f}%)')
            sizes.append(equity_meur)
            colors.append(self.professional_colors['primary_palette'][2])

        # Commercial Debt slice
        if commercial_debt > 0:
            labels.append(f'Commercial Debt\n€{commercial_debt:.1f}M\n({commercial_debt/capex_meur*100:.1f}%)')
            sizes.append(commercial_debt)
            colors.append(self.professional_colors['secondary_palette'][2])

        # SIMEST Soft Loan slice
        if simest_soft > 0:
            labels.append(f'SIMEST Soft Loan\n€{simest_soft:.1f}M\n({simest_soft/capex_meur*100:.1f}%)')
            sizes.append(simest_soft)
            colors.append(self.professional_colors['financial_palette'][5])

        # SIMEST Grant slice
        if simest_grant > 0:
            labels.append(f'SIMEST Grant\n€{simest_grant:.1f}M\n({simest_grant/capex_meur*100:.1f}%)')
            sizes.append(simest_grant)
            colors.append('#009246')  # Italian green

        # Other Grants slice
        if other_grants > 0:
            labels.append(f'Other Grants\n€{other_grants:.1f}M\n({other_grants/capex_meur*100:.1f}%)')
            sizes.append(other_grants)
            colors.append(self.professional_colors['success_palette'][1])

        # Create pie chart
        wedges, texts, autotexts = ax.pie(
            sizes, labels=labels, colors=colors,
            autopct='%1.1f%%', startangle=90, pctdistance=0.85, explode=[0.03]*len(sizes)
        )

        # Beautify text
        for text in texts:
            text.set_fontsize(11)
            text.set_weight('bold')
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontsize(12)
            autotext.set_weight('bold')

        ax.set_title(title, fontsize=16, fontweight='bold')

        # Save if required
        if save_path:
            self._save_chart_to_file(fig, save_path, title)

        chart_bytes = self._get_chart_bytes(fig)
        ui_component = self._create_ui_component(fig, width=900, height=500)
        plt.close(fig)

        return ui_component, chart_bytes

    def create_lcoe_incentive_waterfall(self, 
                                      baseline_lcoe: float,
                                      incentive_impacts: Dict[str, float],
                                      title: str = "LCOE Impact Analysis: Incentives Breakdown",
                                      save_path: Optional[Path] = None) -> Dict[str, Any]:
        """Create LCOE waterfall chart showing impact of each incentive type."""
        fig, ax = plt.subplots(figsize=(14, 8))

        # Prepare waterfall categories and values
        categories = ['Baseline LCOE']
        values = [baseline_lcoe]
        colors = [self.professional_colors['danger_palette'][1]]  # Red baseline

        incentive_names = {
            'simest_grant': 'SIMEST Grant Impact',
            'simest_soft_loan': 'SIMEST Financing Benefit',
            'masen': 'MASEN Commercial\nIncentives',
            'iresen': 'IRESEN R&D\n(Non-Commercial)',
            'connection': 'Grid Connection\nSupport',
            'cri': 'CRI Regional\nSupport'
        }

        running = baseline_lcoe
        for key, impact in incentive_impacts.items():
            if impact == 0:
                continue
            name = incentive_names.get(key, key.replace('_', ' ').title())
            # For reductions, use negative impact
            reduction = -abs(impact)
            running += reduction
            categories.append(name)
            values.append(reduction)
            if key == 'simest_grant':
                colors.append('#009246')  # Italian green
            elif key == 'simest_soft_loan':
                colors.append(self.professional_colors['financial_palette'][5])  # Brown
            else:
                colors.append(self.professional_colors['success_palette'][1])  # Standard green

        # Final
        categories.append('Final LCOE')
        values.append(running)
        colors.append(self.professional_colors['primary_palette'][0])

        # Plot bars
        cum = 0
        for i, (cat, val, col) in enumerate(zip(categories, values, colors)):
            if i == 0:
                ax.bar(i, val, color=col, alpha=0.8)
                cum = val
            else:
                if i == len(categories) - 1:
                    ax.bar(i, val, color=col, alpha=0.8)
                else:
                    ax.bar(i, val, bottom=cum, color=col, alpha=0.8)
                    cum += val
                # Connect
                if i > 0:
                    ax.plot([i-1 + 0.5, i - 0.5], [cum - val, cum - val], 'k--', alpha=0.5)
            # Label
            label_y = val/2 + (cum - val if i not in [0, len(categories)-1] else 0)
            ax.text(i, label_y, f'{abs(val):.2f}', ha='center', va='center', fontsize=10, fontweight='bold')

        ax.set_xticks(range(len(categories)))
        ax.set_xticklabels(categories, rotation=45, ha='right')
        ax.set_ylabel('LCOE (€/MWh)')
        ax.set_title(title, fontsize=14, fontweight='bold')
        ax.grid(True, axis='y', alpha=0.3)

        # Summary
        total_reduction = baseline_lcoe - running
        percent_red = total_reduction / baseline_lcoe * 100 if baseline_lcoe else 0
        summary = f'Total Reduction: {total_reduction:.2f} €/MWh ({percent_red:.1f}%)'
        ax.text(0.02, 0.95, summary, transform=ax.transAxes, fontsize=11,
                bbox=dict(boxstyle='round,pad=0.3', facecolor='lightblue', alpha=0.5),
                verticalalignment='top')

        plt.tight_layout()

        if save_path:
            chart_path = self._get_individual_chart_path(save_path, "lcoe_incentive_waterfall")
            if chart_path:
                self._save_chart_to_file(fig, chart_path, title)

        chart_bytes = self._get_chart_bytes(fig)
        ui_component = self._create_ui_component(fig, width=900, height=600)
        plt.close(fig)

        return {
            'title': title,
            'bytes': chart_bytes,
            'path': chart_path if save_path else None,
            'ui_component': ui_component
        }

    def create_interactive_dashboard_html(self,
                                        analysis_results: Dict[str, Any],
                                        title: str = "Interactive Financial Dashboard") -> str:
        """Create interactive HTML dashboard with Plotly charts."""
        try:
            # Create interactive Plotly charts
            charts_html = []

            # 1. Financial Structure Chart (Pie Chart)
            if 'financial_results' in analysis_results:
                financial_results = analysis_results['financial_results']

                # Extract financial data
                total_capex = financial_results.get('total_capex_meur', 10.0)
                equity = financial_results.get('equity_meur', total_capex * 0.3)
                debt = financial_results.get('debt_meur', total_capex * 0.7)
                grants = financial_results.get('total_grants_meur', 0)

                # Create pie chart
                fig_pie = go.Figure(data=[go.Pie(
                    labels=['Equity', 'Debt', 'Grants'],
                    values=[equity, debt, grants],
                    hole=0.3,
                    marker_colors=['#1f77b4', '#ff7f0e', '#2ca02c']
                )])
                fig_pie.update_layout(
                    title="Financial Structure",
                    font=dict(size=14),
                    showlegend=True
                )
                charts_html.append(fig_pie.to_html(include_plotlyjs=False, div_id="financial_structure"))

            # 2. Cash Flow Timeline (Line Chart)
            if 'cashflow_analysis' in analysis_results:
                cashflow_data = analysis_results['cashflow_analysis']
                years = list(range(1, 21))  # 20 years
                cashflows = [cashflow_data.get(f'year_{i}', 0) for i in years]

                fig_line = go.Figure()
                fig_line.add_trace(go.Scatter(
                    x=years,
                    y=cashflows,
                    mode='lines+markers',
                    name='Cash Flow',
                    line=dict(color='#2ca02c', width=3)
                ))
                fig_line.update_layout(
                    title="Cash Flow Timeline",
                    xaxis_title="Year",
                    yaxis_title="Cash Flow (M€)",
                    font=dict(size=14)
                )
                charts_html.append(fig_line.to_html(include_plotlyjs=False, div_id="cashflow_timeline"))

            # 3. KPI Gauge Chart
            if 'financial_results' in analysis_results:
                financial_results = analysis_results['financial_results']
                irr = financial_results.get('project_irr_percent', 12.0)

                fig_gauge = go.Figure(go.Indicator(
                    mode="gauge+number+delta",
                    value=irr,
                    domain={'x': [0, 1], 'y': [0, 1]},
                    title={'text': "Project IRR (%)"},
                    delta={'reference': 10},
                    gauge={
                        'axis': {'range': [None, 25]},
                        'bar': {'color': "darkblue"},
                        'steps': [
                            {'range': [0, 10], 'color': "lightgray"},
                            {'range': [10, 15], 'color': "yellow"},
                            {'range': [15, 25], 'color': "green"}
                        ],
                        'threshold': {
                            'line': {'color': "red", 'width': 4},
                            'thickness': 0.75,
                            'value': 15
                        }
                    }
                ))
                fig_gauge.update_layout(font=dict(size=14))
                charts_html.append(fig_gauge.to_html(include_plotlyjs=False, div_id="irr_gauge"))

            # Create complete HTML dashboard
            html_template = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>{title}</title>
                <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
                <style>
                    body {{
                        font-family: Arial, sans-serif;
                        margin: 20px;
                        background-color: #f5f5f5;
                    }}
                    .dashboard-header {{
                        text-align: center;
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        color: white;
                        padding: 30px;
                        border-radius: 10px;
                        margin-bottom: 30px;
                    }}
                    .chart-container {{
                        background: white;
                        border-radius: 10px;
                        padding: 20px;
                        margin-bottom: 20px;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    }}
                    .chart-grid {{
                        display: grid;
                        grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
                        gap: 20px;
                    }}
                </style>
            </head>
            <body>
                <div class="dashboard-header">
                    <h1>{title}</h1>
                    <p>Interactive Financial Analysis Dashboard</p>
                </div>

                <div class="chart-grid">
                    {''.join([f'<div class="chart-container">{chart}</div>' for chart in charts_html])}
                </div>

                <div style="text-align: center; margin-top: 30px; color: #666;">
                    <p>Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                </div>
            </body>
            </html>
            """

            return html_template

        except Exception as e:
            self.logger.error(f"Error creating interactive dashboard HTML: {e}")
            # Return a simple error page
            return f"""
            <!DOCTYPE html>
            <html>
            <head><title>Dashboard Error</title></head>
            <body>
                <h1>Dashboard Generation Error</h1>
                <p>Error: {str(e)}</p>
                <p>Please check the logs for more details.</p>
            </body>
            </html>
            """

    def export_chart_collection(self,
                               charts: Dict[str, bytes],
                               output_dir: Path,
                               format: str = 'png',
                               quality: str = 'high') -> List[Path]:
        """Export a collection of charts to files."""
        try:
            exported_files = []
            output_dir.mkdir(parents=True, exist_ok=True)

            for chart_name, chart_bytes in charts.items():
                if chart_bytes:
                    # Create filename
                    filename = f"{chart_name}.{format.lower()}"
                    filepath = output_dir / filename

                    # Write chart bytes to file
                    with open(filepath, 'wb') as f:
                        f.write(chart_bytes)

                    exported_files.append(filepath)
                    self.logger.info(f"Exported chart: {filepath}")

            self.logger.info(f"Successfully exported {len(exported_files)} charts to {output_dir}")
            return exported_files

        except Exception as e:
            self.logger.error(f"Error exporting chart collection: {e}")
            return []

    def create_executive_summary_chart(self,
                                     analysis_results: Dict[str, Any],
                                     title: str = "Executive Summary Dashboard",
                                     save_path: Optional[Path] = None) -> Tuple[ft.Container, bytes]:
        """Create executive summary dashboard chart."""
        try:
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle(title, fontsize=18, fontweight='bold')

            # Extract financial results
            financial_results = analysis_results.get('financial_results', {})

            # Chart 1: Key Financial Metrics (Bar Chart)
            metrics = ['IRR (%)', 'NPV (M€)', 'Payback (years)', 'DSCR']
            values = [
                financial_results.get('project_irr_percent', 12.0),
                financial_results.get('npv_meur', 5.0),
                financial_results.get('payback_years', 8.0),
                financial_results.get('min_dscr', 1.25)
            ]

            bars = ax1.bar(metrics, values, color=self.professional_colors['financial_palette'][:4], alpha=0.8)
            ax1.set_title('Key Financial Metrics', fontweight='bold')
            ax1.set_ylabel('Value')

            # Add value labels on bars
            for bar, value in zip(bars, values):
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                        f'{value:.1f}', ha='center', va='bottom', fontweight='bold')

            # Chart 2: Financial Structure (Pie Chart)
            total_capex = financial_results.get('total_capex_meur', 10.0)
            equity = financial_results.get('equity_meur', total_capex * 0.3)
            debt = financial_results.get('debt_meur', total_capex * 0.7)
            grants = financial_results.get('total_grants_meur', 0)

            sizes = [equity, debt, grants] if grants > 0 else [equity, debt]
            labels = ['Equity', 'Debt', 'Grants'] if grants > 0 else ['Equity', 'Debt']
            colors = self.professional_colors['corporate_palette'][:len(sizes)]

            ax2.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
            ax2.set_title('Financial Structure', fontweight='bold')

            # Chart 3: Cash Flow Timeline (Line Chart)
            years = list(range(1, 11))  # 10 years
            cashflows = [financial_results.get(f'year_{i}_cashflow', i * 0.5) for i in years]

            ax3.plot(years, cashflows, marker='o', linewidth=2.5,
                    color=self.professional_colors['success_palette'][1])
            ax3.set_title('Cash Flow Timeline', fontweight='bold')
            ax3.set_xlabel('Year')
            ax3.set_ylabel('Cash Flow (M€)')
            ax3.grid(True, alpha=0.3)

            # Chart 4: Risk Analysis (Horizontal Bar Chart)
            risk_factors = ['Market Risk', 'Technology Risk', 'Financial Risk', 'Regulatory Risk']
            risk_scores = [3.2, 2.8, 2.1, 3.5]  # Sample risk scores

            bars = ax4.barh(risk_factors, risk_scores, color=self.professional_colors['warning_palette'][:4], alpha=0.8)
            ax4.set_title('Risk Assessment', fontweight='bold')
            ax4.set_xlabel('Risk Score (1-5)')
            ax4.set_xlim(0, 5)

            # Add value labels
            for bar, score in zip(bars, risk_scores):
                width = bar.get_width()
                ax4.text(width + 0.05, bar.get_y() + bar.get_height()/2.,
                        f'{score:.1f}', ha='left', va='center', fontweight='bold')

            plt.tight_layout()

            # Save if required
            if save_path:
                self._save_chart_to_file(fig, save_path, title)

            chart_bytes = self._get_chart_bytes(fig)
            ui_component = self._create_ui_component(fig, width=1200, height=800)
            plt.close(fig)

            return ui_component, chart_bytes

        except Exception as e:
            self.logger.error(f"Error creating executive summary chart: {e}")
            # Return empty chart
            fig, ax = plt.subplots(figsize=(10, 6))
            ax.text(0.5, 0.5, f'Chart Error: {str(e)}', ha='center', va='center', transform=ax.transAxes)
            ax.set_title(title)
            chart_bytes = self._get_chart_bytes(fig)
            ui_component = self._create_ui_component(fig)
            plt.close(fig)
            return ui_component, chart_bytes

    def create_and_export_bar_chart(self,
                                   data: Dict[str, Any],
                                   title: str = "Bar Chart",
                                   x_label: str = "",
                                   y_label: str = "Value",
                                   save_path: Optional[Path] = None) -> Tuple[ft.Container, bytes]:
        """Create and export a bar chart."""
        try:
            fig, ax = plt.subplots(figsize=(12, 8))

            # Extract data for bar chart
            if isinstance(data, dict):
                labels = list(data.keys())
                values = list(data.values())
            else:
                labels = ['Value 1', 'Value 2', 'Value 3']
                values = [10, 20, 15]

            bars = ax.bar(labels, values, color=self.professional_colors['financial_palette'][:len(labels)], alpha=0.8)
            ax.set_title(title, fontsize=16, fontweight='bold')

            # Set axis labels
            if x_label:
                ax.set_xlabel(x_label, fontsize=12, fontweight='bold')
            if y_label:
                ax.set_ylabel(y_label, fontsize=12, fontweight='bold')

            # Add value labels on bars
            for bar, value in zip(bars, values):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                       f'{value:.1f}', ha='center', va='bottom', fontweight='bold')

            plt.xticks(rotation=45, ha='right')
            plt.tight_layout()

            if save_path:
                self._save_chart_to_file(fig, save_path, title)

            chart_bytes = self._get_chart_bytes(fig)
            ui_component = self._create_ui_component(fig, width=900, height=600)
            plt.close(fig)

            return ui_component, chart_bytes

        except Exception as e:
            self.logger.error(f"Error creating bar chart: {e}")
            return self._create_error_chart(title, str(e))

    def create_and_export_line_chart(self,
                                   data,
                                   title: str = "Line Chart",
                                   save_path: Optional[Path] = None,
                                   x_column: Optional[str] = None,
                                   y_columns: Optional[List[str]] = None,
                                   x_label: str = "X Axis",
                                   y_label: str = "Y Axis") -> Tuple[ft.Container, bytes]:
        """Create and export a line chart with support for DataFrame input."""
        try:
            fig, ax = plt.subplots(figsize=(12, 8))

            # Handle different data types
            if isinstance(data, pd.DataFrame):
                # DataFrame input with column specifications
                if x_column and x_column in data.columns:
                    x_data = data[x_column]
                else:
                    x_data = data.index

                if y_columns:
                    # Multiple y columns
                    colors = self.professional_colors['success_palette']
                    for i, col in enumerate(y_columns):
                        if col in data.columns:
                            color = colors[i % len(colors)]
                            ax.plot(x_data, data[col], marker='o', linewidth=2.5,
                                   color=color, label=col.replace('_', ' ').title())
                    ax.legend()
                else:
                    # Single y column (first numeric column)
                    numeric_cols = data.select_dtypes(include=[np.number]).columns
                    if len(numeric_cols) > 0:
                        y_col = numeric_cols[0]
                        ax.plot(x_data, data[y_col], marker='o', linewidth=2.5,
                               color=self.professional_colors['success_palette'][1])
                    else:
                        raise ValueError("No numeric columns found in DataFrame")

            elif isinstance(data, dict) and 'x' in data and 'y' in data:
                # Dictionary input with x and y keys
                x_data = data['x']
                y_data = data['y']
                ax.plot(x_data, y_data, marker='o', linewidth=2.5,
                       color=self.professional_colors['success_palette'][1])
            else:
                # Default sample data
                x_data = list(range(1, 11))
                y_data = [i * 0.5 + np.random.normal(0, 0.1) for i in x_data]
                ax.plot(x_data, y_data, marker='o', linewidth=2.5,
                       color=self.professional_colors['success_palette'][1])

            ax.set_title(title, fontsize=16, fontweight='bold')
            ax.set_xlabel(x_label)
            ax.set_ylabel(y_label)
            ax.grid(True, alpha=0.3)

            plt.tight_layout()

            if save_path:
                self._save_chart_to_file(fig, save_path, title)

            chart_bytes = self._get_chart_bytes(fig)
            ui_component = self._create_ui_component(fig, width=900, height=600)
            plt.close(fig)

            return ui_component, chart_bytes

        except Exception as e:
            self.logger.error(f"Error creating line chart: {e}")
            return self._create_error_chart(title, str(e))

    def create_industry_benchmark_comparison(self,
                                           project_metrics: Dict[str, float],
                                           technology: str = "solar",
                                           region: str = "Central Europe",
                                           title: str = "Industry Benchmark Comparison",
                                           save_path: Optional[Path] = None) -> Tuple[ft.Container, bytes]:
        """Create industry benchmark comparison chart."""
        try:
            fig, ax = plt.subplots(figsize=(12, 8))

            # Sample benchmark data (in a real implementation, this would come from the benchmarks service)
            benchmark_data = {
                'IRR (%)': {'project': project_metrics.get('irr_project', 12.0) * 100, 'benchmark': 14.5},
                'LCOE (€/MWh)': {'project': project_metrics.get('lcoe', 45.0), 'benchmark': 42.0},
                'Capacity Factor (%)': {'project': project_metrics.get('capacity_factor', 0.25) * 100, 'benchmark': 28.0},
                'CAPEX (€/kW)': {'project': project_metrics.get('capex_per_kw', 1200), 'benchmark': 1150},
            }

            metrics = list(benchmark_data.keys())
            project_values = [benchmark_data[metric]['project'] for metric in metrics]
            benchmark_values = [benchmark_data[metric]['benchmark'] for metric in metrics]

            x = np.arange(len(metrics))
            width = 0.35

            bars1 = ax.bar(x - width/2, project_values, width, label='Project',
                          color=self.professional_colors['primary_palette'][0], alpha=0.8)
            bars2 = ax.bar(x + width/2, benchmark_values, width, label=f'{technology.title()} Benchmark',
                          color=self.professional_colors['secondary_palette'][1], alpha=0.8)

            ax.set_xlabel('Metrics', fontsize=12, fontweight='bold')
            ax.set_ylabel('Values', fontsize=12, fontweight='bold')
            ax.set_title(title, fontsize=16, fontweight='bold')
            ax.set_xticks(x)
            ax.set_xticklabels(metrics, rotation=45, ha='right')
            ax.legend()
            ax.grid(True, alpha=0.3)

            # Add value labels on bars
            for bars in [bars1, bars2]:
                for bar in bars:
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                           f'{height:.1f}', ha='center', va='bottom', fontsize=10)

            plt.tight_layout()

            # Save if required
            if save_path:
                self._save_chart_to_file(fig, save_path, title)

            chart_bytes = self._get_chart_bytes(fig)
            ui_component = self._create_ui_component(fig, width=900, height=600)
            plt.close(fig)

            return ui_component, chart_bytes

        except Exception as e:
            self.logger.error(f"Error creating industry benchmark comparison: {e}")
            return self._create_error_chart(title, str(e))

    def create_enhanced_monte_carlo_dashboard(self,
                                            mc_results: Dict[str, Any],
                                            title: str = "Enhanced Monte Carlo Dashboard") -> Dict[str, Any]:
        """Create enhanced Monte Carlo dashboard with multiple charts."""
        try:
            # Extract Monte Carlo data
            results_df = mc_results.get('results', {})
            statistics = mc_results.get('statistics', {})

            # Create main dashboard figure
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle(title, fontsize=18, fontweight='bold')

            # Chart 1: IRR Distribution Histogram
            if 'IRR_equity' in results_df:
                irr_values = [x * 100 for x in results_df['IRR_equity']]
                ax1.hist(irr_values, bins=30, alpha=0.7, color=self.professional_colors['primary_palette'][0])
                ax1.set_title('IRR Distribution', fontweight='bold')
                ax1.set_xlabel('IRR (%)')
                ax1.set_ylabel('Frequency')
                ax1.grid(True, alpha=0.3)

                # Add mean line
                mean_irr = np.mean(irr_values)
                ax1.axvline(mean_irr, color='red', linestyle='--', linewidth=2, label=f'Mean: {mean_irr:.1f}%')
                ax1.legend()
            else:
                ax1.text(0.5, 0.5, 'IRR data not available', ha='center', va='center', transform=ax1.transAxes)
                ax1.set_title('IRR Distribution', fontweight='bold')

            # Chart 2: NPV Distribution Histogram
            if 'NPV_equity' in results_df:
                npv_values = [x / 1000000 for x in results_df['NPV_equity']]  # Convert to millions
                ax2.hist(npv_values, bins=30, alpha=0.7, color=self.professional_colors['success_palette'][1])
                ax2.set_title('NPV Distribution', fontweight='bold')
                ax2.set_xlabel('NPV (M€)')
                ax2.set_ylabel('Frequency')
                ax2.grid(True, alpha=0.3)

                # Add mean line
                mean_npv = np.mean(npv_values)
                ax2.axvline(mean_npv, color='red', linestyle='--', linewidth=2, label=f'Mean: {mean_npv:.1f}M€')
                ax2.legend()
            else:
                ax2.text(0.5, 0.5, 'NPV data not available', ha='center', va='center', transform=ax2.transAxes)
                ax2.set_title('NPV Distribution', fontweight='bold')

            # Chart 3: Risk Metrics
            risk_metrics = ['P10', 'P50', 'P90']
            if 'IRR_equity' in statistics:
                irr_stats = statistics['IRR_equity']
                risk_values = [
                    irr_stats.get('percentile_90', 0.15) * 100,  # P10 (90th percentile)
                    irr_stats.get('percentile_50', 0.12) * 100,  # P50 (median)
                    irr_stats.get('percentile_10', 0.09) * 100   # P90 (10th percentile)
                ]

                bars = ax3.bar(risk_metrics, risk_values, color=self.professional_colors['warning_palette'][:3], alpha=0.8)
                ax3.set_title('IRR Risk Profile', fontweight='bold')
                ax3.set_ylabel('IRR (%)')
                ax3.grid(True, alpha=0.3)

                # Add value labels
                for bar, value in zip(bars, risk_values):
                    height = bar.get_height()
                    ax3.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                            f'{value:.1f}%', ha='center', va='bottom', fontweight='bold')
            else:
                ax3.text(0.5, 0.5, 'Risk metrics not available', ha='center', va='center', transform=ax3.transAxes)
                ax3.set_title('IRR Risk Profile', fontweight='bold')

            # Chart 4: Correlation Matrix (simplified)
            correlation_data = {
                'IRR vs NPV': 0.85,
                'IRR vs LCOE': -0.72,
                'NPV vs CAPEX': -0.68,
                'LCOE vs CF': -0.91
            }

            correlations = list(correlation_data.keys())
            corr_values = list(correlation_data.values())
            colors = [self.professional_colors['success_palette'][1] if v > 0 else self.professional_colors['danger_palette'][1] for v in corr_values]

            bars = ax4.barh(correlations, corr_values, color=colors, alpha=0.8)
            ax4.set_title('Key Correlations', fontweight='bold')
            ax4.set_xlabel('Correlation Coefficient')
            ax4.set_xlim(-1, 1)
            ax4.grid(True, alpha=0.3)

            # Add value labels
            for bar, value in zip(bars, corr_values):
                width = bar.get_width()
                ax4.text(width + (0.05 if width > 0 else -0.05), bar.get_y() + bar.get_height()/2.,
                        f'{value:.2f}', ha='left' if width > 0 else 'right', va='center', fontweight='bold')

            plt.tight_layout()

            chart_bytes = self._get_chart_bytes(fig)
            ui_component = self._create_ui_component(fig, width=1200, height=800)
            plt.close(fig)

            return {
                'ui_component': ui_component,
                'bytes': chart_bytes,
                'title': title
            }

        except Exception as e:
            self.logger.error(f"Error creating enhanced Monte Carlo dashboard: {e}")
            return {
                'ui_component': ft.Container(content=ft.Text(f"Dashboard Error: {str(e)}")),
                'bytes': b'',
                'title': title
            }

    def _create_error_chart(self, title: str, error_msg: str) -> Tuple[ft.Container, bytes]:
        """Create a simple error chart when chart generation fails."""
        try:
            fig, ax = plt.subplots(figsize=(10, 6))
            ax.text(0.5, 0.5, f'Chart Error: {error_msg}', ha='center', va='center',
                   transform=ax.transAxes, fontsize=12, color='red')
            ax.set_title(title, fontsize=16, fontweight='bold')
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.axis('off')

            chart_bytes = self._get_chart_bytes(fig)
            ui_component = self._create_ui_component(fig)
            plt.close(fig)

            return ui_component, chart_bytes
        except:
            # If even error chart fails, return empty
            return ft.Container(content=ft.Text("Chart generation failed")), b''

    # Financial Analysis Chart Methods
    def create_dcf_waterfall_chart(self, *args, **kwargs):
        """Create DCF waterfall chart."""
        return self.financial_charts.create_dcf_waterfall_chart(*args, **kwargs)

    def create_irr_sensitivity_surface(self, *args, **kwargs):
        """Create IRR sensitivity surface chart."""
        return self.financial_charts.create_irr_sensitivity_surface(*args, **kwargs)

    def create_debt_service_coverage_chart(self, *args, **kwargs):
        """Create debt service coverage ratio chart."""
        return self.financial_charts.create_debt_service_coverage_chart(*args, **kwargs)

    def create_financing_structure_comparison(self, *args, **kwargs):
        """Create financing structure comparison chart."""
        return self.financial_charts.create_financing_structure_comparison(*args, **kwargs)

    # Risk & Sensitivity Chart Methods
    def create_sensitivity_heatmap(self, *args, **kwargs):
        """Create sensitivity analysis heatmap."""
        return self.risk_charts.create_sensitivity_heatmap(*args, **kwargs)

    def create_tornado_diagram(self, *args, **kwargs):
        """Create tornado diagram for sensitivity analysis."""
        return self.risk_charts.create_tornado_diagram(*args, **kwargs)

    def create_monte_carlo_distribution(self, *args, **kwargs):
        """Create Monte Carlo distribution chart."""
        return self.risk_charts.create_monte_carlo_distribution(*args, **kwargs)

    def create_risk_dashboard(self, *args, **kwargs):
        """Create comprehensive risk dashboard."""
        return self.risk_charts.create_risk_dashboard(*args, **kwargs)

    # Project Management Chart Methods
    def create_gantt_chart(self, *args, **kwargs):
        """Create Gantt chart for project timeline."""
        return self.project_charts.create_gantt_chart(*args, **kwargs)

    def create_milestone_tracking_chart(self, *args, **kwargs):
        """Create milestone tracking chart."""
        return self.project_charts.create_milestone_tracking_chart(*args, **kwargs)

    def create_resource_allocation_chart(self, *args, **kwargs):
        """Create resource allocation chart."""
        return self.project_charts.create_resource_allocation_chart(*args, **kwargs)

    # Market Analysis Chart Methods
    def create_competitive_positioning_map(self, *args, **kwargs):
        """Create competitive positioning map."""
        return self.market_charts.create_competitive_positioning_map(*args, **kwargs)

    def create_location_comparison_radar(self, *args, **kwargs):
        """Create location comparison radar chart."""
        return self.market_charts.create_location_comparison_radar(*args, **kwargs)

    def create_market_analysis_dashboard(self, *args, **kwargs):
        """Create market analysis dashboard."""
        return self.market_charts.create_market_analysis_dashboard(*args, **kwargs)

    # Basic Chart Methods
    def create_kpi_gauge(self, *args, **kwargs):
        """Create KPI gauge chart."""
        return self.basic_charts.create_kpi_gauge(*args, **kwargs)

    def create_pie_chart(self, *args, **kwargs):
        """Create pie chart."""
        return self.basic_charts.create_pie_chart(*args, **kwargs)

    def create_comparison_table(self, *args, **kwargs):
        """Create comparison table visualization."""
        return self.basic_charts.create_comparison_table(*args, **kwargs)

    def create_scenario_comparison_matrix(self, *args, **kwargs):
        """Create scenario comparison matrix."""
        return self.basic_charts.create_scenario_comparison_matrix(*args, **kwargs)

    def __getattr__(self, name):
        """Handle missing chart methods dynamically."""
        if name.startswith('create_') and name.endswith('_chart'):
            def missing_chart_method(*args, **kwargs):
                """Generic fallback for missing chart methods."""
                title = kwargs.get('title', name.replace('_', ' ').title())
                save_path = kwargs.get('save_path', None)

                self.logger.warning(f"Chart method '{name}' not implemented, creating placeholder chart")

                # Create a simple placeholder chart
                fig, ax = plt.subplots(figsize=(10, 6))
                ax.text(0.5, 0.5, f'Chart "{title}"\n(Method {name} not yet implemented)',
                       ha='center', va='center', transform=ax.transAxes,
                       fontsize=14, bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.7))
                ax.set_title(title, fontsize=16, fontweight='bold')
                ax.axis('off')

                if save_path:
                    self._save_chart_to_file(fig, save_path, title)

                chart_bytes = self._get_chart_bytes(fig)
                ui_component = self._create_ui_component(fig)
                plt.close(fig)

                # Return format depends on what the caller expects
                if 'dashboard' in name:
                    return {'ui_component': ui_component, 'bytes': chart_bytes}
                else:
                    return ui_component, chart_bytes

            return missing_chart_method

        # For non-chart methods, raise AttributeError as normal
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")