"""
Project Management Charts
========================

Specialized chart components for project management including Gantt charts,
milestone tracking, and resource allocation visualizations.
"""

import flet as ft
from typing import Dict, Any, Optional, List, Tuple
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')
import matplotlib.dates as mdates
import numpy as np
import pandas as pd
import io
import base64
from pathlib import Path
import logging
from datetime import datetime, timedelta


class ProjectManagementCharts:
    """Project management chart components."""
    
    def __init__(self, chart_factory):
        self.chart_factory = chart_factory
        self.logger = logging.getLogger(__name__)
        
        # Use chart factory's styling and export settings
        self.professional_colors = chart_factory.professional_colors
        self.professional_style = chart_factory.professional_style
        self.export_settings = chart_factory.export_settings
    
    def create_gantt_chart(self, 
                          project_timeline: List[Dict] = None, 
                          title: str = "Project Timeline",
                          save_path: Optional[Path] = None) -> Tuple[ft.Container, bytes]:
        """Create Gantt chart for project timeline visualization."""
        try:
            fig, ax = plt.subplots(figsize=(14, 8))
            
            # Sample project timeline if none provided
            if not project_timeline:
                base_date = datetime(2024, 1, 1)
                project_timeline = [
                    {'task': 'Project Planning', 'start': base_date, 'duration': 30, 'progress': 100},
                    {'task': 'Permits & Approvals', 'start': base_date + timedelta(days=15), 'duration': 90, 'progress': 75},
                    {'task': 'Equipment Procurement', 'start': base_date + timedelta(days=60), 'duration': 120, 'progress': 50},
                    {'task': 'Site Preparation', 'start': base_date + timedelta(days=105), 'duration': 45, 'progress': 25},
                    {'task': 'Construction', 'start': base_date + timedelta(days=150), 'duration': 180, 'progress': 10},
                    {'task': 'Testing & Commissioning', 'start': base_date + timedelta(days=300), 'duration': 60, 'progress': 0},
                    {'task': 'Commercial Operation', 'start': base_date + timedelta(days=360), 'duration': 30, 'progress': 0}
                ]
            
            # Prepare data
            tasks = [item['task'] for item in project_timeline]
            starts = [item['start'] for item in project_timeline]
            durations = [item['duration'] for item in project_timeline]
            progress = [item.get('progress', 0) for item in project_timeline]
            
            # Create Gantt bars
            y_pos = np.arange(len(tasks))
            
            for i, (start, duration, prog) in enumerate(zip(starts, durations, progress)):
                # Total duration bar (light color)
                ax.barh(i, duration, left=start, height=0.6, 
                       color=self.professional_colors['secondary_palette'][2], alpha=0.3)
                
                # Progress bar (darker color)
                completed_duration = duration * (prog / 100)
                if completed_duration > 0:
                    ax.barh(i, completed_duration, left=start, height=0.6,
                           color=self.professional_colors['success_palette'][1], alpha=0.8)
                
                # Add progress percentage
                ax.text(start + timedelta(days=duration/2), i, f'{prog}%', 
                       ha='center', va='center', fontweight='bold', fontsize=9)
            
            # Add today's date line
            today = datetime.now()
            ax.axvline(x=today, color='red', linestyle='--', linewidth=2, label='Today')
            
            # Formatting
            ax.set_yticks(y_pos)
            ax.set_yticklabels(tasks)
            ax.set_xlabel('Timeline', fontsize=12, fontweight='bold')
            ax.set_title(title, fontsize=16, fontweight='bold')
            ax.legend()
            ax.grid(True, alpha=0.3, axis='x')
            
            # Format x-axis dates
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
            ax.xaxis.set_major_locator(mdates.MonthLocator(interval=2))
            plt.xticks(rotation=45)
            
            plt.tight_layout()
            
            if save_path:
                self.chart_factory._save_chart_to_file(fig, save_path, title)
            
            chart_bytes = self.chart_factory._get_chart_bytes(fig)
            ui_component = self.chart_factory._create_ui_component(fig, width=1200, height=600)
            plt.close(fig)
            
            return ui_component, chart_bytes
            
        except Exception as e:
            self.logger.error(f"Error creating Gantt chart: {e}")
            return self.chart_factory._create_error_chart(title, str(e))
    
    def create_milestone_tracking_chart(self,
                                      milestones: List[Dict] = None,
                                      title: str = "Milestone Tracking",
                                      save_path: Optional[Path] = None) -> Tuple[ft.Container, bytes]:
        """Create milestone tracking chart."""
        try:
            fig, ax = plt.subplots(figsize=(12, 8))
            
            # Sample milestones if none provided
            if not milestones:
                base_date = datetime(2024, 1, 1)
                milestones = [
                    {'name': 'Project Approval', 'planned': base_date, 'actual': base_date + timedelta(days=5), 'status': 'completed'},
                    {'name': 'Permits Obtained', 'planned': base_date + timedelta(days=90), 'actual': base_date + timedelta(days=95), 'status': 'completed'},
                    {'name': 'Equipment Delivery', 'planned': base_date + timedelta(days=180), 'actual': None, 'status': 'in_progress'},
                    {'name': 'Construction Start', 'planned': base_date + timedelta(days=210), 'actual': None, 'status': 'pending'},
                    {'name': 'Grid Connection', 'planned': base_date + timedelta(days=330), 'actual': None, 'status': 'pending'},
                    {'name': 'Commercial Operation', 'planned': base_date + timedelta(days=365), 'actual': None, 'status': 'pending'}
                ]
            
            # Prepare data
            milestone_names = [m['name'] for m in milestones]
            planned_dates = [m['planned'] for m in milestones]
            actual_dates = [m.get('actual') for m in milestones]
            statuses = [m['status'] for m in milestones]
            
            y_pos = np.arange(len(milestone_names))
            
            # Color mapping for status
            status_colors = {
                'completed': self.professional_colors['success_palette'][1],
                'in_progress': self.professional_colors['warning_palette'][1],
                'pending': self.professional_colors['secondary_palette'][2],
                'delayed': self.professional_colors['danger_palette'][1]
            }
            
            # Plot planned dates
            for i, (planned, status) in enumerate(zip(planned_dates, statuses)):
                ax.scatter(planned, i, color=status_colors[status], s=200, alpha=0.7, 
                          marker='o', label='Planned' if i == 0 else "")
            
            # Plot actual dates where available
            for i, (actual, status) in enumerate(zip(actual_dates, statuses)):
                if actual:
                    ax.scatter(actual, i, color=status_colors[status], s=200, 
                              marker='s', label='Actual' if i == 0 else "")
                    
                    # Draw line between planned and actual
                    ax.plot([planned_dates[i], actual], [i, i], 
                           color='gray', linestyle='--', alpha=0.5)
            
            # Add today's date line
            today = datetime.now()
            ax.axvline(x=today, color='red', linestyle='-', linewidth=2, label='Today')
            
            # Formatting
            ax.set_yticks(y_pos)
            ax.set_yticklabels(milestone_names)
            ax.set_xlabel('Date', fontsize=12, fontweight='bold')
            ax.set_title(title, fontsize=16, fontweight='bold')
            ax.legend()
            ax.grid(True, alpha=0.3, axis='x')
            
            # Format x-axis dates
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
            ax.xaxis.set_major_locator(mdates.MonthLocator(interval=2))
            plt.xticks(rotation=45)
            
            plt.tight_layout()
            
            if save_path:
                self.chart_factory._save_chart_to_file(fig, save_path, title)
            
            chart_bytes = self.chart_factory._get_chart_bytes(fig)
            ui_component = self.chart_factory._create_ui_component(fig, width=1000, height=600)
            plt.close(fig)
            
            return ui_component, chart_bytes
            
        except Exception as e:
            self.logger.error(f"Error creating milestone tracking chart: {e}")
            return self.chart_factory._create_error_chart(title, str(e))
    
    def create_resource_allocation_chart(self,
                                       resource_data: Dict[str, Dict] = None,
                                       title: str = "Resource Allocation",
                                       save_path: Optional[Path] = None) -> Tuple[ft.Container, bytes]:
        """Create resource allocation chart."""
        try:
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))
            
            # Sample resource data if none provided
            if not resource_data:
                resource_data = {
                    'Human Resources': {
                        'Project Manager': 1,
                        'Engineers': 5,
                        'Technicians': 8,
                        'Construction Workers': 15,
                        'Quality Control': 2
                    },
                    'Equipment': {
                        'Excavators': 3,
                        'Cranes': 2,
                        'Transport Vehicles': 6,
                        'Testing Equipment': 4,
                        'Safety Equipment': 10
                    }
                }
            
            # Chart 1: Human Resources Pie Chart
            hr_data = resource_data.get('Human Resources', {})
            if hr_data:
                labels = list(hr_data.keys())
                sizes = list(hr_data.values())
                colors = self.professional_colors['primary_palette'][:len(labels)]
                
                wedges, texts, autotexts = ax1.pie(sizes, labels=labels, colors=colors, 
                                                  autopct='%1.1f%%', startangle=90)
                
                for autotext in autotexts:
                    autotext.set_color('white')
                    autotext.set_fontweight('bold')
                
                ax1.set_title('Human Resources\nAllocation', fontsize=14, fontweight='bold')
            
            # Chart 2: Equipment Resources Bar Chart
            equipment_data = resource_data.get('Equipment', {})
            if equipment_data:
                equipment_names = list(equipment_data.keys())
                equipment_counts = list(equipment_data.values())
                
                bars = ax2.bar(equipment_names, equipment_counts, 
                              color=self.professional_colors['secondary_palette'][:len(equipment_names)], 
                              alpha=0.8)
                
                # Add value labels on bars
                for bar, count in zip(bars, equipment_counts):
                    height = bar.get_height()
                    ax2.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                            f'{count}', ha='center', va='bottom', fontweight='bold')
                
                ax2.set_xlabel('Equipment Type', fontsize=12, fontweight='bold')
                ax2.set_ylabel('Quantity', fontsize=12, fontweight='bold')
                ax2.set_title('Equipment Resources\nAllocation', fontsize=14, fontweight='bold')
                ax2.grid(True, alpha=0.3, axis='y')
                
                # Rotate x-axis labels for better readability
                plt.setp(ax2.get_xticklabels(), rotation=45, ha='right')
            
            fig.suptitle(title, fontsize=16, fontweight='bold')
            plt.tight_layout()
            
            if save_path:
                self.chart_factory._save_chart_to_file(fig, save_path, title)
            
            chart_bytes = self.chart_factory._get_chart_bytes(fig)
            ui_component = self.chart_factory._create_ui_component(fig, width=1200, height=600)
            plt.close(fig)
            
            return ui_component, chart_bytes
            
        except Exception as e:
            self.logger.error(f"Error creating resource allocation chart: {e}")
            return self.chart_factory._create_error_chart(title, str(e))
