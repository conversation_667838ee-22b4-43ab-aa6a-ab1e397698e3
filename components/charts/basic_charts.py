"""
Basic Charts
===========

Basic chart components including KPI gauges, pie charts, comparison tables,
and scenario matrices.
"""

import flet as ft
from typing import Dict, Any, Optional, List, Tuple
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')
import numpy as np
import pandas as pd
import io
import base64
from pathlib import Path
import logging
from datetime import datetime


class BasicCharts:
    """Basic chart components."""
    
    def __init__(self, chart_factory):
        self.chart_factory = chart_factory
        self.logger = logging.getLogger(__name__)
        
        # Use chart factory's styling and export settings
        self.professional_colors = chart_factory.professional_colors
        self.professional_style = chart_factory.professional_style
        self.export_settings = chart_factory.export_settings
    
    def create_kpi_gauge(self, 
                        title: str, 
                        current_value: float, 
                        target_value: float = None, 
                        max_value: float = None,
                        save_path: Optional[Path] = None) -> Tuple[ft.Container, bytes]:
        """Create KPI gauge chart."""
        try:
            fig, ax = plt.subplots(figsize=(8, 8), subplot_kw=dict(projection='polar'))
            
            # Set default values
            if target_value is None:
                target_value = current_value * 1.2
            if max_value is None:
                max_value = target_value * 1.5
            
            # Gauge parameters
            theta_min = np.pi
            theta_max = 2 * np.pi
            theta_range = theta_max - theta_min
            
            # Calculate angles
            current_angle = theta_min + (current_value / max_value) * theta_range
            target_angle = theta_min + (target_value / max_value) * theta_range
            
            # Create gauge background
            theta_bg = np.linspace(theta_min, theta_max, 100)
            r_bg = np.ones_like(theta_bg)
            ax.plot(theta_bg, r_bg, color='lightgray', linewidth=20, alpha=0.3)
            
            # Create value arc
            theta_val = np.linspace(theta_min, current_angle, 100)
            r_val = np.ones_like(theta_val)
            
            # Color based on performance vs target
            if current_value >= target_value:
                color = self.professional_colors['success_palette'][1]
            elif current_value >= target_value * 0.8:
                color = self.professional_colors['warning_palette'][1]
            else:
                color = self.professional_colors['danger_palette'][1]
            
            ax.plot(theta_val, r_val, color=color, linewidth=20)
            
            # Add target marker
            ax.plot([target_angle, target_angle], [0.8, 1.2], 
                   color='red', linewidth=4, label=f'Target: {target_value:.1f}')
            
            # Add current value pointer
            ax.plot([current_angle, current_angle], [0, 1], 
                   color='black', linewidth=3)
            ax.scatter([current_angle], [1], color='black', s=100, zorder=5)
            
            # Add value text in center
            ax.text(0, 0, f'{current_value:.1f}', ha='center', va='center', 
                   fontsize=24, fontweight='bold', transform=ax.transData)
            
            # Formatting
            ax.set_ylim(0, 1.5)
            ax.set_theta_zero_location('S')
            ax.set_theta_direction(1)
            ax.set_thetagrids([])
            ax.set_rgrids([])
            ax.spines['polar'].set_visible(False)
            ax.set_title(title, fontsize=16, fontweight='bold', pad=30)
            
            plt.tight_layout()
            
            if save_path:
                self.chart_factory._save_chart_to_file(fig, save_path, title)
            
            chart_bytes = self.chart_factory._get_chart_bytes(fig)
            ui_component = self.chart_factory._create_ui_component(fig, width=600, height=600)
            plt.close(fig)
            
            return ui_component, chart_bytes
            
        except Exception as e:
            self.logger.error(f"Error creating KPI gauge: {e}")
            return self.chart_factory._create_error_chart(title, str(e))
    
    def create_pie_chart(self, 
                        data: Dict[str, float], 
                        title: str,
                        save_path: Optional[Path] = None) -> Tuple[ft.Container, bytes]:
        """Create pie chart."""
        try:
            fig, ax = plt.subplots(figsize=(10, 8))
            
            # Extract data
            labels = list(data.keys())
            sizes = list(data.values())
            
            # Use professional colors
            colors = self.professional_colors['primary_palette'][:len(labels)]
            if len(labels) > len(colors):
                colors.extend(self.professional_colors['secondary_palette'][:len(labels)-len(colors)])
            
            # Create pie chart
            wedges, texts, autotexts = ax.pie(sizes, labels=labels, colors=colors,
                                             autopct='%1.1f%%', startangle=90,
                                             explode=[0.05] * len(labels))
            
            # Beautify text
            for text in texts:
                text.set_fontsize(12)
                text.set_fontweight('bold')
            
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontsize(11)
                autotext.set_fontweight('bold')
            
            ax.set_title(title, fontsize=16, fontweight='bold')
            
            plt.tight_layout()
            
            if save_path:
                self.chart_factory._save_chart_to_file(fig, save_path, title)
            
            chart_bytes = self.chart_factory._get_chart_bytes(fig)
            ui_component = self.chart_factory._create_ui_component(fig, width=800, height=600)
            plt.close(fig)
            
            return ui_component, chart_bytes
            
        except Exception as e:
            self.logger.error(f"Error creating pie chart: {e}")
            return self.chart_factory._create_error_chart(title, str(e))
    
    def create_comparison_table(self,
                              comparison_data: Dict[str, Dict[str, Any]] = None,
                              title: str = "Comparison Table",
                              save_path: Optional[Path] = None) -> Tuple[ft.Container, bytes]:
        """Create comparison table visualization."""
        try:
            fig, ax = plt.subplots(figsize=(12, 8))
            
            # Sample comparison data if none provided
            if not comparison_data:
                comparison_data = {
                    'Scenario A': {'IRR': '15.2%', 'NPV': '€8.5M', 'Payback': '7.2 years', 'Risk': 'Medium'},
                    'Scenario B': {'IRR': '12.8%', 'NPV': '€6.2M', 'Payback': '8.5 years', 'Risk': 'Low'},
                    'Scenario C': {'IRR': '18.1%', 'NPV': '€11.3M', 'Payback': '6.1 years', 'Risk': 'High'}
                }
            
            # Convert to DataFrame for easier handling
            df = pd.DataFrame(comparison_data).T
            
            # Create table
            table_data = []
            table_data.append(list(df.columns))  # Headers
            for index, row in df.iterrows():
                table_data.append([index] + list(row.values))
            
            # Create table plot
            ax.axis('tight')
            ax.axis('off')
            
            table = ax.table(cellText=table_data[1:], colLabels=['Scenario'] + list(df.columns),
                           cellLoc='center', loc='center')
            
            # Style the table
            table.auto_set_font_size(False)
            table.set_fontsize(11)
            table.scale(1.2, 2)
            
            # Color header row
            for i in range(len(df.columns) + 1):
                table[(0, i)].set_facecolor(self.professional_colors['primary_palette'][0])
                table[(0, i)].set_text_props(weight='bold', color='white')
            
            # Color data rows alternately
            for i in range(1, len(table_data)):
                color = self.professional_colors['background_light'] if i % 2 == 0 else 'white'
                for j in range(len(df.columns) + 1):
                    table[(i, j)].set_facecolor(color)
            
            ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
            
            plt.tight_layout()
            
            if save_path:
                self.chart_factory._save_chart_to_file(fig, save_path, title)
            
            chart_bytes = self.chart_factory._get_chart_bytes(fig)
            ui_component = self.chart_factory._create_ui_component(fig, width=1000, height=600)
            plt.close(fig)
            
            return ui_component, chart_bytes
            
        except Exception as e:
            self.logger.error(f"Error creating comparison table: {e}")
            return self.chart_factory._create_error_chart(title, str(e))
    
    def create_scenario_comparison_matrix(self,
                                        scenario_data: Dict[str, Dict[str, float]] = None,
                                        title: str = "Scenario Comparison Matrix",
                                        save_path: Optional[Path] = None) -> Tuple[ft.Container, bytes]:
        """Create scenario comparison matrix heatmap."""
        try:
            fig, ax = plt.subplots(figsize=(12, 8))
            
            # Sample scenario data if none provided
            if not scenario_data:
                scenario_data = {
                    'Base Case': {'IRR': 15.2, 'NPV': 8.5, 'DSCR': 1.35, 'Payback': 7.2},
                    'Optimistic': {'IRR': 18.7, 'NPV': 12.8, 'DSCR': 1.65, 'Payback': 6.1},
                    'Pessimistic': {'IRR': 11.8, 'NPV': 4.2, 'DSCR': 1.15, 'Payback': 9.5},
                    'High CAPEX': {'IRR': 13.1, 'NPV': 6.1, 'DSCR': 1.25, 'Payback': 8.3},
                    'Low Revenue': {'IRR': 12.5, 'NPV': 5.8, 'DSCR': 1.20, 'Payback': 8.8}
                }
            
            # Convert to DataFrame
            df = pd.DataFrame(scenario_data).T
            
            # Normalize data for heatmap (0-1 scale for each metric)
            df_norm = df.copy()
            for col in df.columns:
                if col == 'Payback':  # Lower is better for payback
                    df_norm[col] = 1 - (df[col] - df[col].min()) / (df[col].max() - df[col].min())
                else:  # Higher is better for other metrics
                    df_norm[col] = (df[col] - df[col].min()) / (df[col].max() - df[col].min())
            
            # Create heatmap
            im = ax.imshow(df_norm.values, cmap='RdYlGn', aspect='auto', vmin=0, vmax=1)
            
            # Set ticks and labels
            ax.set_xticks(np.arange(len(df.columns)))
            ax.set_yticks(np.arange(len(df.index)))
            ax.set_xticklabels(df.columns)
            ax.set_yticklabels(df.index)
            
            # Add text annotations with actual values
            for i in range(len(df.index)):
                for j in range(len(df.columns)):
                    value = df.iloc[i, j]
                    if df.columns[j] == 'IRR':
                        text = f'{value:.1f}%'
                    elif df.columns[j] == 'NPV':
                        text = f'€{value:.1f}M'
                    elif df.columns[j] == 'Payback':
                        text = f'{value:.1f}y'
                    else:
                        text = f'{value:.2f}'
                    
                    ax.text(j, i, text, ha="center", va="center", 
                           color="black" if df_norm.iloc[i, j] > 0.5 else "white",
                           fontweight='bold')
            
            # Add colorbar
            cbar = plt.colorbar(im, ax=ax)
            cbar.set_label('Performance Score', rotation=270, labelpad=20)
            
            ax.set_title(title, fontsize=16, fontweight='bold')
            
            plt.tight_layout()
            
            if save_path:
                self.chart_factory._save_chart_to_file(fig, save_path, title)
            
            chart_bytes = self.chart_factory._get_chart_bytes(fig)
            ui_component = self.chart_factory._create_ui_component(fig, width=1000, height=600)
            plt.close(fig)
            
            return ui_component, chart_bytes
            
        except Exception as e:
            self.logger.error(f"Error creating scenario comparison matrix: {e}")
            return self.chart_factory._create_error_chart(title, str(e))
