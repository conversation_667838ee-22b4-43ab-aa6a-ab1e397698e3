"""
Financial Analysis Charts
========================

Specialized chart components for financial analysis including DCF waterfalls,
IRR sensitivity surfaces, debt service coverage, and financing comparisons.
"""

import flet as ft
from typing import Dict, Any, Optional, List, Tuple
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')
import numpy as np
import pandas as pd
import io
import base64
from pathlib import Path
import logging
from datetime import datetime


class FinancialAnalysisCharts:
    """Financial analysis chart components."""
    
    def __init__(self, chart_factory):
        self.chart_factory = chart_factory
        self.logger = logging.getLogger(__name__)
        
        # Use chart factory's styling and export settings
        self.professional_colors = chart_factory.professional_colors
        self.professional_style = chart_factory.professional_style
        self.export_settings = chart_factory.export_settings
    
    def create_dcf_waterfall_chart(self, 
                                 cash_flows: Dict[str, float] = None, 
                                 title: str = "DCF Analysis",
                                 save_path: Optional[Path] = None) -> Tuple[ft.Container, bytes]:
        """Create DCF waterfall chart showing cash flow components."""
        try:
            fig, ax = plt.subplots(figsize=(14, 8))
            
            # Sample DCF data if none provided
            if not cash_flows:
                cash_flows = {
                    'Initial Investment': -100.0,
                    'Year 1-5 Operations': 45.0,
                    'Year 6-10 Operations': 35.0,
                    'Year 11-15 Operations': 25.0,
                    'Year 16-20 Operations': 20.0,
                    'Terminal Value': 15.0
                }
            
            categories = list(cash_flows.keys())
            values = list(cash_flows.values())
            
            # Calculate cumulative values for waterfall
            cumulative = [0]
            for i, val in enumerate(values):
                if i == 0:
                    cumulative.append(val)
                else:
                    cumulative.append(cumulative[-1] + val)
            
            # Create waterfall chart
            colors = []
            for val in values:
                if val < 0:
                    colors.append(self.professional_colors['danger_palette'][1])
                else:
                    colors.append(self.professional_colors['success_palette'][1])
            
            # Plot bars
            for i, (cat, val) in enumerate(zip(categories, values)):
                if i == 0:
                    # Initial investment (starts from 0)
                    ax.bar(i, val, color=colors[i], alpha=0.8)
                else:
                    # Subsequent cash flows (start from previous cumulative)
                    ax.bar(i, val, bottom=cumulative[i], color=colors[i], alpha=0.8)
                
                # Add connecting lines
                if i > 0:
                    ax.plot([i-0.4, i-0.4], [cumulative[i], cumulative[i+1]], 'k--', alpha=0.5)
                
                # Add value labels
                label_y = val/2 + (cumulative[i] if i > 0 else 0)
                ax.text(i, label_y, f'€{val:.1f}M', ha='center', va='center', 
                       fontweight='bold', fontsize=10)
            
            # Final NPV bar
            final_npv = cumulative[-1]
            ax.bar(len(categories), final_npv, 
                  color=self.professional_colors['primary_palette'][0], alpha=0.8)
            ax.text(len(categories), final_npv/2, f'NPV\n€{final_npv:.1f}M', 
                   ha='center', va='center', fontweight='bold', fontsize=11)
            
            # Formatting
            ax.set_xticks(range(len(categories) + 1))
            ax.set_xticklabels(categories + ['Net Present Value'], rotation=45, ha='right')
            ax.set_ylabel('Cash Flow (€ Millions)', fontsize=12, fontweight='bold')
            ax.set_title(title, fontsize=16, fontweight='bold')
            ax.grid(True, alpha=0.3)
            ax.axhline(y=0, color='black', linewidth=0.8)
            
            plt.tight_layout()
            
            if save_path:
                self.chart_factory._save_chart_to_file(fig, save_path, title)
            
            chart_bytes = self.chart_factory._get_chart_bytes(fig)
            ui_component = self.chart_factory._create_ui_component(fig, width=1000, height=600)
            plt.close(fig)
            
            return ui_component, chart_bytes
            
        except Exception as e:
            self.logger.error(f"Error creating DCF waterfall chart: {e}")
            return self.chart_factory._create_error_chart(title, str(e))
    
    def create_irr_sensitivity_surface(self,
                                     sensitivity_data: Dict[str, Any] = None,
                                     title: str = "IRR Sensitivity Surface",
                                     save_path: Optional[Path] = None) -> Tuple[ft.Container, bytes]:
        """Create 3D surface plot for IRR sensitivity analysis."""
        try:
            fig = plt.figure(figsize=(12, 8))
            ax = fig.add_subplot(111, projection='3d')
            
            # Sample sensitivity data if none provided
            if not sensitivity_data:
                # Create sample data for CAPEX vs Revenue sensitivity
                capex_range = np.linspace(0.8, 1.2, 20)  # 80% to 120% of base case
                revenue_range = np.linspace(0.8, 1.2, 20)  # 80% to 120% of base case
                X, Y = np.meshgrid(capex_range, revenue_range)
                
                # IRR calculation (simplified model)
                base_irr = 0.15  # 15% base IRR
                Z = base_irr * (Y / X) * 0.8  # Simplified sensitivity
            else:
                X = sensitivity_data.get('x_values', np.linspace(0.8, 1.2, 20))
                Y = sensitivity_data.get('y_values', np.linspace(0.8, 1.2, 20))
                Z = sensitivity_data.get('irr_values', np.random.rand(20, 20) * 0.3)
            
            # Create surface plot
            surf = ax.plot_surface(X, Y, Z, cmap='viridis', alpha=0.8)
            
            # Add contour lines
            ax.contour(X, Y, Z, zdir='z', offset=Z.min(), cmap='viridis', alpha=0.5)
            
            # Labels and title
            ax.set_xlabel('CAPEX Multiplier', fontsize=12, fontweight='bold')
            ax.set_ylabel('Revenue Multiplier', fontsize=12, fontweight='bold')
            ax.set_zlabel('IRR', fontsize=12, fontweight='bold')
            ax.set_title(title, fontsize=16, fontweight='bold')
            
            # Add colorbar
            fig.colorbar(surf, shrink=0.5, aspect=5)
            
            plt.tight_layout()
            
            if save_path:
                self.chart_factory._save_chart_to_file(fig, save_path, title)
            
            chart_bytes = self.chart_factory._get_chart_bytes(fig)
            ui_component = self.chart_factory._create_ui_component(fig, width=1000, height=700)
            plt.close(fig)
            
            return ui_component, chart_bytes
            
        except Exception as e:
            self.logger.error(f"Error creating IRR sensitivity surface: {e}")
            return self.chart_factory._create_error_chart(title, str(e))
    
    def create_debt_service_coverage_chart(self,
                                         dscr_data: Dict[str, float] = None,
                                         title: str = "Debt Service Coverage Ratio",
                                         save_path: Optional[Path] = None) -> Tuple[ft.Container, bytes]:
        """Create DSCR chart showing debt coverage over time."""
        try:
            fig, ax = plt.subplots(figsize=(12, 8))
            
            # Sample DSCR data if none provided
            if not dscr_data:
                years = list(range(2024, 2044))  # 20 years
                dscr_values = [1.2 + 0.1 * np.sin(i/3) + np.random.normal(0, 0.05) for i in range(20)]
                dscr_data = dict(zip([f"Year {y}" for y in years], dscr_values))
            
            years = list(dscr_data.keys())
            dscr_values = list(dscr_data.values())
            
            # Create line chart
            line = ax.plot(years, dscr_values, marker='o', linewidth=3, markersize=6,
                          color=self.professional_colors['primary_palette'][0])
            
            # Add minimum DSCR threshold line
            min_dscr = 1.2
            ax.axhline(y=min_dscr, color=self.professional_colors['danger_palette'][1], 
                      linestyle='--', linewidth=2, label=f'Minimum DSCR ({min_dscr})')
            
            # Color coding based on DSCR level
            for i, (year, dscr) in enumerate(zip(years, dscr_values)):
                color = (self.professional_colors['success_palette'][1] if dscr >= min_dscr 
                        else self.professional_colors['danger_palette'][1])
                ax.scatter(year, dscr, color=color, s=100, zorder=5)
            
            # Formatting
            ax.set_xlabel('Year', fontsize=12, fontweight='bold')
            ax.set_ylabel('DSCR', fontsize=12, fontweight='bold')
            ax.set_title(title, fontsize=16, fontweight='bold')
            ax.grid(True, alpha=0.3)
            ax.legend()
            
            # Rotate x-axis labels for better readability
            plt.xticks(rotation=45, ha='right')
            plt.tight_layout()
            
            if save_path:
                self.chart_factory._save_chart_to_file(fig, save_path, title)
            
            chart_bytes = self.chart_factory._get_chart_bytes(fig)
            ui_component = self.chart_factory._create_ui_component(fig, width=1000, height=600)
            plt.close(fig)
            
            return ui_component, chart_bytes
            
        except Exception as e:
            self.logger.error(f"Error creating DSCR chart: {e}")
            return self.chart_factory._create_error_chart(title, str(e))
    
    def create_financing_structure_comparison(self,
                                            scenarios: Dict[str, Dict[str, float]] = None,
                                            title: str = "Financing Structure Comparison",
                                            save_path: Optional[Path] = None) -> Tuple[ft.Container, bytes]:
        """Create comparison chart for different financing structures."""
        try:
            fig, ax = plt.subplots(figsize=(12, 8))
            
            # Sample financing scenarios if none provided
            if not scenarios:
                scenarios = {
                    'Conservative': {'Equity': 40, 'Debt': 50, 'Grants': 10},
                    'Balanced': {'Equity': 30, 'Debt': 60, 'Grants': 10},
                    'Aggressive': {'Equity': 20, 'Debt': 70, 'Grants': 10},
                    'Grant-Heavy': {'Equity': 25, 'Debt': 50, 'Grants': 25}
                }
            
            scenario_names = list(scenarios.keys())
            financing_types = list(scenarios[scenario_names[0]].keys())
            
            # Create stacked bar chart
            bottom = np.zeros(len(scenario_names))
            colors = [
                self.professional_colors['primary_palette'][0],
                self.professional_colors['secondary_palette'][1],
                self.professional_colors['success_palette'][1]
            ]
            
            for i, fin_type in enumerate(financing_types):
                values = [scenarios[scenario][fin_type] for scenario in scenario_names]
                bars = ax.bar(scenario_names, values, bottom=bottom, 
                             label=fin_type, color=colors[i % len(colors)], alpha=0.8)
                
                # Add percentage labels
                for j, (bar, value) in enumerate(zip(bars, values)):
                    if value > 5:  # Only show label if segment is large enough
                        height = bar.get_height()
                        ax.text(bar.get_x() + bar.get_width()/2., 
                               bottom[j] + height/2., f'{value:.0f}%',
                               ha='center', va='center', fontweight='bold', fontsize=10)
                
                bottom += values
            
            # Formatting
            ax.set_xlabel('Financing Scenario', fontsize=12, fontweight='bold')
            ax.set_ylabel('Percentage (%)', fontsize=12, fontweight='bold')
            ax.set_title(title, fontsize=16, fontweight='bold')
            ax.legend(loc='upper right')
            ax.grid(True, alpha=0.3, axis='y')
            ax.set_ylim(0, 100)
            
            plt.tight_layout()
            
            if save_path:
                self.chart_factory._save_chart_to_file(fig, save_path, title)
            
            chart_bytes = self.chart_factory._get_chart_bytes(fig)
            ui_component = self.chart_factory._create_ui_component(fig, width=1000, height=600)
            plt.close(fig)
            
            return ui_component, chart_bytes
            
        except Exception as e:
            self.logger.error(f"Error creating financing structure comparison: {e}")
            return self.chart_factory._create_error_chart(title, str(e))
